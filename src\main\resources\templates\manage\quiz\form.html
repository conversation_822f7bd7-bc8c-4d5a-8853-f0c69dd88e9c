<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title th:text="${pageTitle} + ' - 한림공원 관리시스템'">문제 관리 - 한림공원 관리시스템</title>
    <meta name="description" th:content="${pageDescription}">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/quiz/form.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 메인 콘텐츠 -->
        <main class="quiz-form-main">
        <div class="quiz-form-container">
            <!-- 페이지 헤더 -->
            <div class="quiz-form-header">
                <h1 th:text="${pageTitle}">문제 등록</h1>
                <p th:text="${pageDescription}">새로운 문제를 등록합니다.</p>
            </div>

            <!-- 문제 등록/수정 폼 -->
            <form id="quizForm" class="quiz-form">
                <!-- 기본 정보 섹션 -->
                <div class="quiz-form-section">
                    <h2>기본 정보</h2>
                    
                    <!-- 문제 제목 -->
                    <div class="quiz-form-field">
                        <label for="title" class="quiz-form-label">문제 제목 <span class="required">*</span></label>
                        <input type="text" id="title" name="title" class="quiz-form-input"
                               th:value="${quiz?.title}" placeholder="문제 제목을 입력하세요">
                    </div>

                    <!-- 카테고리 설정 -->
                    <div class="quiz-form-field">
                        <label class="quiz-form-label">카테고리 설정 <span class="required">*</span></label>
                        <div class="quiz-form-category-container">
                            <!-- 최상위 카테고리 -->
                            <div class="quiz-form-category-field">
                                <label for="parentCategory" class="quiz-form-sublabel">최상위 카테고리</label>
                                <select id="parentCategory" name="parentCategory" class="quiz-form-select">
                                    <option value="">최상위 카테고리를 선택하세요</option>
                                    <option th:each="category : ${rootCategories}"
                                            th:value="${category.categoryId}"
                                            th:text="${category.categoryName}"
                                            th:selected="${quiz?.parentCategoryName != null and quiz.parentCategoryName == category.categoryName}">
                                        카테고리명
                                    </option>
                                </select>
                            </div>
                            
                            <!-- 하위 카테고리 -->
                            <div class="quiz-form-category-field">
                                <label for="categoryId" class="quiz-form-sublabel">하위 카테고리</label>
                                <select id="categoryId" name="categoryId" class="quiz-form-select"
                                        th:data-selected-value="${quiz?.categoryId}">
                                    <option value="">하위 카테고리를 선택하세요</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 게임 유형 -->
                    <div class="quiz-form-field">
                        <label for="quizType" class="quiz-form-label">게임 유형 <span class="required">*</span></label>
                        <select id="quizType" name="quizType" class="quiz-form-select">
                            <option value="">게임 유형을 선택하세요</option>
                            <option value="MCQ" th:selected="${quiz?.quizType == 'MCQ'}">객관식</option>
                            <option value="OX" th:selected="${quiz?.quizType == 'OX'}">OX 퀴즈</option>
                            <option value="ORDER" th:selected="${quiz?.quizType == 'ORDER'}">순서 정렬</option>
                            <option value="IMAGE_VOICE" th:selected="${quiz?.quizType == 'IMAGE_VOICE'}">이미지/음성 인식</option>
                            <option value="PUZZLE_MEMORY" th:selected="${quiz?.quizType == 'PUZZLE_MEMORY'}">퍼즐/기억력 게임</option>
                        </select>
                    </div>

                    <!-- 상태 -->
                    <div class="quiz-form-field">
                        <label for="status" class="quiz-form-label">상태</label>
                        <select id="status" name="status" class="quiz-form-select">
                            <option value="ACTIVE" th:selected="${quiz?.status == 'ACTIVE' or quiz?.status == null}">활성</option>
                            <option value="INACTIVE" th:selected="${quiz?.status == 'INACTIVE'}">비활성</option>
                        </select>
                    </div>
                </div>

                <!-- 다국어 설정 섹션 -->
                <div class="quiz-form-section">
                    <h2>다국어 설정</h2>
                    
                    <!-- 언어 선택 -->
                    <div class="quiz-form-field">
                        <label class="quiz-form-label">지원 언어 선택</label>
                        <div class="quiz-form-language-selector">
                            <label class="quiz-form-checkbox-label">
                                <input type="checkbox" name="languages" value="ko" checked data-lang="ko">
                                <span>한국어</span>
                            </label>
                            <label class="quiz-form-checkbox-label">
                                <input type="checkbox" name="languages" value="en" data-lang="en">
                                <span>영어</span>
                            </label>
                            <label class="quiz-form-checkbox-label">
                                <input type="checkbox" name="languages" value="ja" data-lang="ja">
                                <span>일본어</span>
                            </label>
                            <label class="quiz-form-checkbox-label">
                                <input type="checkbox" name="languages" value="zh" data-lang="zh">
                                <span>중국어</span>
                            </label>
                        </div>
                    </div>

                    <!-- 언어별 입력 필드 -->
                    <div id="languageInputs" class="quiz-form-language-inputs">
                        <!-- 한국어 (기본) -->
                        <div class="quiz-form-language-input" data-lang="ko">
                            <label for="question_ko" class="quiz-form-label">한국어 문제 내용 <span class="required">*</span></label>
                            <textarea id="question_ko" name="question_ko" class="quiz-form-textarea"
                                      th:text="${quiz?.question}" placeholder="한국어 문제 내용을 입력하세요"></textarea>
                        </div>
                        
                        <!-- 영어 -->
                        <div class="quiz-form-language-input" data-lang="en" style="display: none;">
                            <label for="question_en" class="quiz-form-label">영어 문제 내용</label>
                            <textarea id="question_en" name="question_en" class="quiz-form-textarea" 
                                      placeholder="Enter question content in English"></textarea>
                        </div>
                        
                        <!-- 일본어 -->
                        <div class="quiz-form-language-input" data-lang="ja" style="display: none;">
                            <label for="question_ja" class="quiz-form-label">일본어 문제 내용</label>
                            <textarea id="question_ja" name="question_ja" class="quiz-form-textarea" 
                                      placeholder="日本語で問題内容を入力してください"></textarea>
                        </div>
                        
                        <!-- 중국어 -->
                        <div class="quiz-form-language-input" data-lang="zh" style="display: none;">
                            <label for="question_zh" class="quiz-form-label">중국어 문제 내용</label>
                            <textarea id="question_zh" name="question_zh" class="quiz-form-textarea" 
                                      placeholder="请输入中文问题内容"></textarea>
                        </div>
                    </div>
                </div>

                <!-- QR 연동 섹션 -->
                <div class="quiz-form-section">
                    <h2>QR 연동</h2>
                    <div class="quiz-form-field">
                        <button type="button" id="qrMappingBtn" class="quiz-form-qr-btn">
                            QR 연동 관리
                        </button>
                        <p class="quiz-form-help-text">문제와 QR 코드를 연동할 수 있습니다.</p>
                    </div>
                </div>

                <!-- 버튼 영역 -->
                <div class="quiz-form-actions">
                    <button type="button" id="backBtn" class="quiz-form-btn quiz-form-btn-secondary">
                        목록으로
                    </button>
                    
                    <!-- 수정 모드일 때만 삭제 버튼 표시 -->
                    <button type="button" id="deleteBtn" class="quiz-form-btn quiz-form-btn-danger" 
                            th:if="${isEdit}" th:data-quiz-id="${quizId}">
                        삭제
                    </button>
                    
                    <button type="button" id="submitBtn" class="quiz-form-btn quiz-form-btn-primary">
                        <span th:text="${isEdit ? '수정' : '등록'}">등록</span>
                    </button>
                </div>
            </form>
        </div>
    </main>
    </div>
</body>

<th:block layout:fragment="scripts">
    <!-- JavaScript -->
    <script src="/js/manage/quiz/form.js"></script>

    <!-- CSRF 토큰 설정 -->
    <script th:inline="javascript">
        window.csrfToken = /*[[${_csrf.token}]]*/ '';
        window.csrfHeader = /*[[${_csrf.headerName}]]*/ '';
        window.isEdit = /*[[${isEdit}]]*/ false;
        window.quizId = /*[[${quizId}]]*/ null;
    </script>
</th:block>
</html>
