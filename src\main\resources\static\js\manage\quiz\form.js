/**
 * 문제 등록/수정 폼 JavaScript
 */

$(document).ready(function() {
    initializeForm();
    bindEvents();
    
    // 수정 모드인 경우 기존 데이터 로드
    if (window.isEdit && window.quizId) {
        loadExistingQuizData();
    }
});

/**
 * 폼 초기화
 */
function initializeForm() {
    // CSRF 토큰 설정
    $.ajaxSetup({
        beforeSend: function(xhr) {
            xhr.setRequestHeader(window.csrfHeader, window.csrfToken);
        }
    });
    
    // 언어 입력 필드 초기화
    updateLanguageInputs();
}

/**
 * 이벤트 바인딩
 */
function bindEvents() {
    // 최상위 카테고리 변경 시 하위 카테고리 로드
    $('#parentCategory').on('change', function() {
        const parentId = $(this).val();
        loadChildCategories(parentId);
    });
    
    // 언어 체크박스 변경 시 입력 필드 업데이트
    $('input[name="languages"]').on('change', function() {
        updateLanguageInputs();
    });
    
    // 폼 제출
    $('#quizForm').on('submit', function(e) {
        e.preventDefault();
        submitForm();
    });
    
    // 목록으로 버튼
    $('#backBtn').on('click', function() {
        window.location.href = '/manage/quiz';
    });
    
    // 삭제 버튼 (수정 모드에서만)
    $('#deleteBtn').on('click', function() {
        const quizId = $(this).data('quiz-id');
        deleteQuiz(quizId);
    });
    
    // QR 연동 버튼
    $('#qrMappingBtn').on('click', function() {
        alert('QR 연동 기능은 추후 구현 예정입니다.');
    });
}

/**
 * 하위 카테고리 로드
 */
function loadChildCategories(parentId) {
    const $childSelect = $('#categoryId');
    
    // 하위 카테고리 선택 박스 초기화
    $childSelect.empty().append('<option value="">하위 카테고리를 선택하세요</option>');
    
    if (!parentId) {
        return;
    }
    
    // AJAX로 하위 카테고리 조회
    $.ajax({
        url: `/manage/quiz/category/${parentId}/children`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                response.data.forEach(function(category) {
                    $childSelect.append(`<option value="${category.categoryId}">${category.categoryName}</option>`);
                });
            } else {
                console.error('하위 카테고리 로드 실패:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('하위 카테고리 로드 중 오류 발생:', error);
            alert('하위 카테고리를 불러오는 중 오류가 발생했습니다.');
        }
    });
}

/**
 * 언어별 입력 필드 업데이트
 */
function updateLanguageInputs() {
    const selectedLanguages = [];
    $('input[name="languages"]:checked').each(function() {
        selectedLanguages.push($(this).val());
    });
    
    // 모든 언어 입력 필드 숨기기
    $('.quiz-form-language-input').hide();
    
    // 선택된 언어의 입력 필드만 표시
    selectedLanguages.forEach(function(lang) {
        $(`.quiz-form-language-input[data-lang="${lang}"]`).show();
    });
    
    // 한국어는 항상 필수
    if (!selectedLanguages.includes('ko')) {
        $('input[name="languages"][value="ko"]').prop('checked', true);
        $('.quiz-form-language-input[data-lang="ko"]').show();
    }
}

/**
 * 기존 문제 데이터 로드 (수정 모드)
 */
function loadExistingQuizData() {
    // 카테고리 정보가 있는 경우 하위 카테고리 로드
    const parentCategoryId = $('#parentCategory').val();
    if (parentCategoryId) {
        // 하위 카테고리 로드
        $.ajax({
            url: `/manage/quiz/category/${parentCategoryId}/children`,
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const $childSelect = $('#categoryId');
                    response.data.forEach(function(category) {
                        $childSelect.append(`<option value="${category.categoryId}">${category.categoryName}</option>`);
                    });

                    // 기존 선택된 카테고리 설정
                    const selectedCategoryId = $childSelect.data('selected-value');
                    if (selectedCategoryId) {
                        $childSelect.val(selectedCategoryId);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('하위 카테고리 로드 중 오류 발생:', error);
            }
        });
    }
}

/**
 * 폼 제출
 */
function submitForm() {
    if (!validateForm()) {
        return;
    }
    
    const formData = collectFormData();
    const url = window.isEdit ? `/manage/quiz/${window.quizId}` : '/manage/quiz/add';
    const method = 'POST';
    
    // 버튼 비활성화
    $('#submitBtn').prop('disabled', true).text('처리 중...');
    
    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert(response.message || (window.isEdit ? '문제가 성공적으로 수정되었습니다.' : '문제가 성공적으로 등록되었습니다.'));
                window.location.href = '/manage/quiz';
            } else {
                alert(response.message || '처리 중 오류가 발생했습니다.');
            }
        },
        error: function(xhr, status, error) {
            console.error('폼 제출 중 오류 발생:', error);
            let errorMessage = '처리 중 오류가 발생했습니다.';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            alert(errorMessage);
        },
        complete: function() {
            // 버튼 활성화
            $('#submitBtn').prop('disabled', false).text(window.isEdit ? '수정' : '등록');
        }
    });
}

/**
 * 폼 데이터 수집
 */
function collectFormData() {
    const formData = {
        question: $('#question').val().trim(),
        categoryId: parseInt($('#categoryId').val()),
        quizType: $('#quizType').val(),
        status: $('#status').val(),
        difficultyLevel: 1, // 기본값
        correctAnswer: '', // 추후 구현
        imageUrl: '', // 추후 구현
        hint: '', // 추후 구현
        deleteYn: 'N'
    };
    
    // 다국어 콘텐츠 수집
    const languages = [];
    $('input[name="languages"]:checked').each(function() {
        const lang = $(this).val();
        const question = $(`#question_${lang}`).val();
        if (question && question.trim()) {
            languages.push({
                langCode: lang,
                question: question.trim(),
                options: null // 추후 구현
            });
        }
    });
    
    formData.languages = languages;
    
    return formData;
}

/**
 * 폼 유효성 검사
 */
function validateForm() {
    // 필수 필드 검사
    const requiredFields = [
        { id: '#question', name: '키워드명(문제제목)' },
        { id: '#categoryId', name: '하위 카테고리' },
        { id: '#quizType', name: '게임 유형' },
        { id: '#question_ko', name: '한국어 문제 내용' }
    ];

    // 모든 필드의 에러 상태 초기화
    requiredFields.forEach(function(field) {
        $(field.id).removeClass('quiz-form-error');
    });

    // 필수 필드 검사
    for (let i = 0; i < requiredFields.length; i++) {
        const field = requiredFields[i];
        const $field = $(field.id);
        const value = $field.val();

        if (!value || value.trim() === '') {
            $field.addClass('quiz-form-error');
            alert(`${field.name}은(는) 필수 입력 항목입니다.`);
            $field.focus();
            return false;
        }
    }

    return true;
}

/**
 * 문제 삭제
 */
function deleteQuiz(quizId) {
    if (!confirm('정말로 이 문제를 삭제하시겠습니까?\n삭제된 문제는 복구할 수 없습니다.')) {
        return;
    }
    
    $.ajax({
        url: `/manage/quiz/${quizId}`,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                alert(response.message || '문제가 성공적으로 삭제되었습니다.');
                window.location.href = '/manage/quiz';
            } else {
                alert(response.message || '삭제 중 오류가 발생했습니다.');
            }
        },
        error: function(xhr, status, error) {
            console.error('삭제 중 오류 발생:', error);
            let errorMessage = '삭제 중 오류가 발생했습니다.';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            alert(errorMessage);
        }
    });
}
