<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizContentMapper">

    <!-- 기본 ResultMap -->
    <resultMap id="QuizContentResultMap" type="kr.wayplus.qr_hallimpark.model.QuizContent">
        <id property="quizContentId" column="quiz_content_id"/>
        <result property="quizId" column="quiz_id"/>
        <result property="langCode" column="lang_code"/>
        <result property="question" column="question"/>
        <result property="options" column="options"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
    </resultMap>

    <!-- 문제 ID로 모든 언어의 콘텐츠 조회 -->
    <select id="selectQuizContentsByQuizId" parameterType="long" resultMap="QuizContentResultMap">
        SELECT 
            quiz_content_id,
            quiz_id,
            lang_code,
            question,
            options,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_content
        WHERE quiz_id = #{quizId}
        AND delete_yn = 'N'
        ORDER BY lang_code
    </select>

    <!-- 문제 ID와 언어 코드로 콘텐츠 조회 -->
    <select id="selectQuizContentByQuizIdAndLangCode" resultMap="QuizContentResultMap">
        SELECT 
            quiz_content_id,
            quiz_id,
            lang_code,
            question,
            options,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_content
        WHERE quiz_id = #{quizId}
        AND lang_code = #{langCode}
        AND delete_yn = 'N'
    </select>

    <!-- 콘텐츠 등록 -->
    <insert id="insertQuizContent" parameterType="kr.wayplus.qr_hallimpark.model.QuizContent" useGeneratedKeys="true" keyProperty="quizContentId">
        INSERT INTO quiz_content (
            quiz_id,
            lang_code,
            question,
            options,
            create_id,
            delete_yn
        ) VALUES (
            #{quizId},
            #{langCode},
            #{question},
            #{options},
            #{createId},
            'N'
        )
    </insert>

    <!-- 콘텐츠 수정 -->
    <update id="updateQuizContent" parameterType="kr.wayplus.qr_hallimpark.model.QuizContent">
        UPDATE quiz_content
        SET
            question = #{question},
            options = #{options},
            last_update_id = #{lastUpdateId}
        WHERE quiz_content_id = #{quizContentId}
        AND delete_yn = 'N'
    </update>

    <!-- 콘텐츠 삭제 (논리 삭제) -->
    <update id="deleteQuizContent">
        UPDATE quiz_content
        SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE quiz_content_id = #{quizContentId}
        AND delete_yn = 'N'
    </update>

    <!-- 문제 ID로 모든 콘텐츠 삭제 (논리 삭제) -->
    <update id="deleteQuizContentsByQuizId">
        UPDATE quiz_content
        SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE quiz_id = #{quizId}
        AND delete_yn = 'N'
    </update>

    <!-- 문제 ID와 언어 코드로 콘텐츠 존재 여부 확인 -->
    <select id="countQuizContentByQuizIdAndLangCode" resultType="int">
        SELECT COUNT(*)
        FROM quiz_content
        WHERE quiz_id = #{quizId}
        AND lang_code = #{langCode}
        AND delete_yn = 'N'
    </select>

    <!-- 문제 질문 내용으로 중복 체크 -->
    <select id="countDuplicateQuestionByLangCode" resultType="int">
        SELECT COUNT(*)
        FROM quiz_content qc
        INNER JOIN quiz_master qm ON qc.quiz_id = qm.quiz_id
        WHERE qc.question = #{question}
        AND qc.lang_code = #{langCode}
        AND qc.delete_yn = 'N'
        AND qm.delete_yn = 'N'
        <if test="excludeQuizId != null">
            AND qc.quiz_id != #{excludeQuizId}
        </if>
    </select>

</mapper>
