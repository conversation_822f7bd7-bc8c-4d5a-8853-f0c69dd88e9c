package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.model.Quiz;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 문제 매퍼
 * - quiz_master, quiz_content, quiz_category 테이블 조인 처리
 * - 공통 리스트 기능 지원
 */
@Mapper
@Repository
public interface QuizMapper extends AdminListMapper<Quiz> {
    
    /**
     * 모든 문제 목록 조회 (카테고리 정보 포함)
     * @return 문제 목록
     */
    List<Quiz> selectQuizList();
    
    /**
     * 문제 ID로 문제 조회
     * @param quizId 문제 ID
     * @return 문제 정보
     */
    Quiz selectQuizById(Long quizId);
    
    /**
     * 특정 카테고리의 문제 목록 조회
     * @param categoryId 카테고리 ID
     * @return 문제 목록
     */
    List<Quiz> selectQuizListByCategoryId(Long categoryId);
    
    /**
     * 특정 카테고리와 그 하위 카테고리의 문제 목록 조회
     * @param categoryId 상위 카테고리 ID
     * @return 문제 목록
     */
    List<Quiz> selectQuizListByCategoryIdWithChildren(Long categoryId);
    
    /**
     * 문제 등록
     * @param quiz 문제 정보
     * @return 등록된 행 수
     */
    int insertQuiz(Quiz quiz);
    
    /**
     * 문제 수정
     * @param quiz 문제 정보
     * @return 수정된 행 수
     */
    int updateQuiz(Quiz quiz);
    
    /**
     * 문제 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteQuiz(@Param("quizId") Long quizId, @Param("deleteId") String deleteId);
    
    /**
     * 문제명으로 중복 체크
     * @param question 문제 질문
     * @param quizId 제외할 문제 ID (수정 시 사용)
     * @return 중복된 문제 개수
     */
    int countDuplicateQuestion(@Param("question") String question, @Param("excludeQuizId") Long quizId);
    
    // ========== 통계 및 집계 관련 메서드 ==========
    
    /**
     * 카테고리별 문제 개수 조회
     * @param categoryId 카테고리 ID
     * @return 문제 개수
     */
    int countQuizByCategoryId(Long categoryId);
    
    /**
     * 문제 유형별 개수 조회
     * @return 문제 유형별 개수 맵
     */
    List<Quiz> selectQuizCountByType();
    

}
